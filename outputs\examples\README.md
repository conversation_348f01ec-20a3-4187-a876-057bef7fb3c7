# MangaGen Examples

This directory contains example outputs from the MangaGen system to demonstrate its capabilities.

## 📁 Directory Structure

### `sample_panels/`
Individual manga panels generated from simple prompts:
- `ninja_dodging_kunai.png` - Action scene with dynamic pose
- `girl_umbrella_rain.png` - Atmospheric scene with shoujo style
- `boy_jumping_rooftop.png` - Urban action scene

### `single_panel_example/`
Example of single panel generation with metadata:
- `panel.png` - Generated manga panel
- `metadata.json` - Generation parameters and settings

### `ninja_magic_sword_manga/`
Complete manga generation example:
- `story_structure.json` - Full story breakdown with chapters and scenes
- `chapter_01/` - Generated panels for Chapter 1
  - `scene_01.png` - Opening scene
  - `scene_02.png` - Character introduction
  - `scene_03.png` - Training sequence
  - `scene_04.png` - Mentor guidance

## 🎨 Generation Details

All examples were generated using:
- **Model**: DeepSeek-V2 for story generation
- **Image Engine**: ComfyUI with manga-optimized workflows
- **Style Detection**: Automatic genre and pose detection
- **Quality**: High-resolution (512x768) manga panels

## 📊 File Sizes

Real generated images typically range from 600KB to 800KB, indicating high-quality output with proper detail and compression.

## 🚀 How to Generate Similar Content

**Single Panel:**
```bash
python scripts/generate_from_prompt.py "your scene description"
```

**Complete Manga:**
```bash
python scripts/generate_full_manga.py "your story idea" --chapters 3
```

## 🔧 Technical Notes

- All images are generated in manga format (512x768 resolution)
- Automatic style detection based on content analysis
- Pose detection from text descriptions
- Metadata saved for reproducibility
- Organized output structure for easy management
